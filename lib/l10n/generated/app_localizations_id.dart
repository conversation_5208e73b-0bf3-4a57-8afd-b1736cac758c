// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Indonesian (`id`).
class AppLocalizationsId extends AppLocalizations {
  AppLocalizationsId([String locale = 'id']) : super(locale);

  @override
  String get appTitle => 'Self Eng';

  @override
  String get home => 'Beranda';

  @override
  String get library => 'Pustaka';

  @override
  String get games => 'Permainan';

  @override
  String get profile => 'Profil';

  @override
  String get counterPage => 'Counter page';

  @override
  String get buttonPushedTimes => 'You have pushed the button this many times:';

  @override
  String get increment => 'Increment';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get imageNotAvailable => 'Gambar tidak ada';

  @override
  String get onboarding1 => '<PERSON><PERSON>n\nBahasa Inggrismu!🚀';

  @override
  String get onboarding2 => 'Belajar Bahasa Inggris\nTanpa Batas!🤳';

  @override
  String get onboarding3 => '<PERSON><PERSON>ng!⏩';

  @override
  String get onboardingSingup => 'Daftar melalui';

  @override
  String get signingIn => 'Sedang masuk...';

  @override
  String get pleaseWait => 'Mohon tunggu sementara kami mengautentikasi Anda';

  @override
  String get selectLanguage => 'Pilih Bahasa';

  @override
  String get selectLanguageDes =>
      'Silakan pilih bahasa yang ingin kamu gunakan.';

  @override
  String get languageIndo => 'Bahasa Indonesia';

  @override
  String get languageEngl => 'Bahasa Inggris';

  @override
  String get choose => 'Pilih';

  @override
  String get other => 'Lainnya';

  @override
  String get next => 'Selanjutnya';

  @override
  String get previous => 'Sebalumnya';

  @override
  String get send => 'Kirim';

  @override
  String get later => 'Nanti saja';

  @override
  String get shortDescription => 'Deskripsikan secara singkat dan jelas';

  @override
  String get questionnaireOtherQ => 'Sebutkan tujuan utama kamu yang lain:';

  @override
  String get questionnaireOtherA => 'Ketik secara singkat';

  @override
  String get questionnaireFinish =>
      'Terima kasih telah mengisi kuesioner penilaian diri!';

  @override
  String get questionnaireFinishDesc =>
      'Tanggapanmu akan membantu menyesuaikan rencana pembelajaran dalam memenuhi kebutuhan, minat, dan tujuan secara spesifik. Mari kita memulai perjalanan belajar bahasa Inggris ini bersama-sama! 🚀📚✨🧑‍🤝‍🧑🌏';

  @override
  String get questionnaireIWilling => 'Ya, ayo mulai';

  @override
  String get please_type => 'Ketik di sini';

  @override
  String get please_type_number => 'Hanya masukkan angka';

  @override
  String get doIt => 'Kerjakan';

  @override
  String get instruction => 'Petunjuk';

  @override
  String get testInstruction => 'Instruksi Tes';

  @override
  String get testInstructionDesc =>
      'Tarik napas dalam-dalam, fokus, dan baca petunjuk dengan cermat sebelum mengerjakan soal';

  @override
  String get testInstructionRule =>
      '1. Kamu memiliki waktu 15 menit untuk menyelesaikan tes.\n2.Bacalah setiap pertanyaan dengan cermat dan pilih jawaban yang paling tepat.\n3. Pilih opsi yang paling mencerminkan pengetahuan atau pemahamanmu.\n4. Tidak ada penalti untuk menebak, jadi usahakan menjawab semua pertanyaan dalam waktu yang ditentukan.\n5. Setelah waktunya habis, tes akan otomatis berakhir, dan tanggapan kamu akan dievaluasi.\n6. Harap jangan merujuk pada sumber eksternal apa pun atau berkonsultasi dengan orang lain selama pengujian.\n7. Di akhir tes, kamu akan menerima umpan balik mengenai kinerja dan rekomendasi untuk tingkat kemahiranmu dalam program berbicara terpadu belajar mandiri.';

  @override
  String get diagnosticTests => 'Tes Diagnostik';

  @override
  String get areYouReady => 'Apakah kamu sudah siap?';

  @override
  String get yesIAmReady => 'Ya, sudah siap.';

  @override
  String get sorry_not_ready_yet => 'Maaf belum siap.';

  @override
  String get areYouSure => 'Apakah kamu sudah yakin?';

  @override
  String get yesIAmSure => 'Ya, sudah yakin';

  @override
  String get notYetLater => 'Belum, nanti dulu';

  @override
  String get continue1 => 'Lanjut';

  @override
  String get timesUpStudyGroup1 =>
      'Yah, waktu pengerjaan tes sudah habis 😔. Klik tombol ';

  @override
  String get timesUpStudyGroup2 => '‘lanjut’';

  @override
  String get timesUpStudyGroup3 => ' untuk mengetahui kelompok belajar anda';

  @override
  String get finallyResult => 'Akhirnya, hasil tes kamu sudah keluar 🎉';

  @override
  String get processingTime => 'Waktu pengerjaan';

  @override
  String get totalScore => 'Skor Total';

  @override
  String get learningLevel => 'Level Belajar';

  @override
  String get finallyResultDesc =>
      'Selamat belajar, ambil langkah pemahamanmu dan nikmati setiap momen dalam perjalanan ini!';

  @override
  String get questionnaireOnboarding1 =>
      'Selamat datang di Tes Diagnostik untuk Program Belajar Mandiri Berbicara Terpadu!';

  @override
  String get questionnaireOnboarding2 =>
      'Tes ini bertujuan untuk menilai tingkat kemahiran kamu dalam berbicara bahasa Inggris dalam berbagai tahap';

  @override
  String get questionnaireOnboarding3 =>
      'Kinerjamu dalam tes ini akan membantu menentukan tingkat program yang paling sesuai dengan kebutuhanmu';

  @override
  String get start => 'Mulai';

  @override
  String get point => 'Poin';

  @override
  String get level => 'Tingkat';

  @override
  String get information => 'Keterangan';

  @override
  String get questionnaireCongrat1 =>
      'Temukan dunia melalui keajaiban bahasa Inggris!';

  @override
  String get questionnaireCongrat1Desc =>
      'Baik itu kamu baru memulai perjalanan atau ingin mencapai tingkat kefasihan baru, sekarang adalah waktu yang tepat untuk memulai.';

  @override
  String get questionnaireCongrat2 =>
      'Mari kita memulai petualangan transformatif ini bersama-sama,';

  @override
  String get questionnaireCongrat2Desc =>
      'Petualangan di mana setiap kata yang dipelajari adalah satu langkah lebih dekat menuju peluang yang menarik dan pengalaman tanpa batas';

  @override
  String get questionnaireCongrat3 =>
      'Nyalakan semangatmu terhadap bahasa Inggris hari ini.';

  @override
  String get questionnaireCongrat3Desc =>
      'Apakah kamu siap untuk menjalani perjalanan ini? Mari selami dan wujudkan impian belajar bahasa Inggrismu!📚🚀';

  @override
  String get pronunciationChallenge => 'Tantangan Pengucapan';

  @override
  String get conversationVideo => 'Video Percakapan';

  @override
  String get listeningMastery => 'Penguasaan Menyimak';

  @override
  String get speakingArena => 'Arena Berbicara';

  @override
  String get record => 'Rekam';

  @override
  String get stage1Speaking => 'Simak dan ikuti audio serta skrip';

  @override
  String get stage1SpeakingDesc => 'Simak audio dan baca skrip dengan cermat.';

  @override
  String get stage2Speaking => 'Anda Berperan\nsebagai Penanya';

  @override
  String get stage3Speaking => 'Anda Berperan\nsebagai Penjawab';

  @override
  String get nextSection => 'Siap menghadapi tantangan selanjutnya?';

  @override
  String hiUser(String userName) {
    return 'Hai, $userName 👋';
  }

  @override
  String get welcome => 'Selamat datang!';

  @override
  String get welcome_back => 'Selamat datang kembali! Apa kabar?🥳';

  @override
  String get back => 'Kembali';

  @override
  String get seeAll => 'Lihat semua';

  @override
  String get selectedLanguageDesc1 => 'Kamu telah memilih';

  @override
  String get express_yourself_in_english =>
      'Siap ngomong bahasa Inggris? Ayo mulai! 💬';

  @override
  String get join_the_community => 'Gabung Komunitas';

  @override
  String get instructions => 'Petunjuk';

  @override
  String get listen_and_imitate => 'Dengarkan dan Tiru';

  @override
  String get listen_and_imitate_desc =>
      'Dengarkan rekaman pengucapan, fokus pada suara dan tekanan, lalu tiru dengan lantang.';

  @override
  String get record_and_analyze => 'Rekam dan Tingkatkan';

  @override
  String get record_and_analyze_desc =>
      'Rekam pengucapanmu, dapatkan analisis AI, bandingkan dengan model, dan sesuaikan hingga sempurna.';

  @override
  String get compare_and_adjust => 'Bandingkan dan Sesuaikan';

  @override
  String get compare_and_adjust_desc =>
      'Bandingkan pengucapanmu dengan model yang direkam dan buat penyesuaian yang diperlukan berdasarkan umpan baliknya.';

  @override
  String get practice_and_perfect => 'Berlatih dan Sempurnakan';

  @override
  String get practice_and_perfect_desc =>
      'Teruslah berlatih dan menyempurnakan pengucapan sampai kamu merasa percaya diri dalam mengucapkan setiap kata dan ekspresi secara akurat.';

  @override
  String get watch_and_analyze => 'Tonton dan Analisis';

  @override
  String get watch_and_analyze_desc1 =>
      'Tonton video dengan teks aktif, ulangi bagian penting, dan fokus pada kosakata, tata bahasa, serta tema percakapan.';

  @override
  String get watch_and_analyze_desc2 =>
      'Tonton ulang segmen, berhenti sejenak untuk fokus pada bagian tertentu, dan bagi percakapan menjadi bagian-bagian yang lebih kecil.';

  @override
  String get focus_on_vocabulary_and_grammar =>
      'Fokus pada Kosakata dan Tata Bahasa';

  @override
  String get focus_on_vocabulary_and_grammar_desc1 =>
      'Gunakan keterangan untuk memperkuat pemahaman, kosa kata, dan struktur tata bahasa.';

  @override
  String get focus_on_vocabulary_and_grammar_desc2 =>
      'Catat kata atau frasa asing dan artinya.';

  @override
  String get pronunciation_practice => 'Latihan Pengucapan';

  @override
  String get pronunciation_practice_desc1 =>
      'Mendengarkan dan meniru pengucapan dan intonasi pembicara.';

  @override
  String get pronunciation_practice_desc2 =>
      'Perhatikan pola stres, ritme, dan keterkaitan kata, dengan menggunakan teks sebagai alat bantu visual.';

  @override
  String get reflect_and_review => 'Latihan dan Tinjau';

  @override
  String get reflect_and_review_desc1 =>
      'Tirukan pengucapan, perhatikan pola intonasi, dan tinjau catatan untuk memperbaiki area yang perlu ditingkatkan.';

  @override
  String get reflect_and_review_desc2 =>
      'Tinjau catatan dan identifikasi area yang memerlukan perbaikan, dengan mempertimbangkan tujuan dan minat pembelajaran bahasa Anda.';

  @override
  String get listen_actively => 'Dengarkan dan Catat';

  @override
  String get listen_actively_desc =>
      'Gunakan headphone/speaker, dengarkan secara aktif, dan buat catatan untuk memahami rekaman.';

  @override
  String get repeat_and_review => 'Ulangi dan Tinjau';

  @override
  String get repeat_and_review_desc =>
      'Dengarkan rekamannya beberapa kali, jeda dan putar ulang sesuai kebutuhan.';

  @override
  String get answer_the_questions => 'Jawab Pertanyaan';

  @override
  String get answer_the_questions_desc =>
      'Baca pertanyaan dengan cermat dan jawab berdasarkan informasi yang diberikan dalam percakapan.';

  @override
  String get submit_and_review => 'Jawab dan Tinjau';

  @override
  String get submit_and_review_desc =>
      'Pilih jawaban yang tersedia, kirim untuk evaluasi, dan tinjau masukan untuk meningkatkan keterampilan menyimak kamu.';

  @override
  String get listen_and_follow => 'Dengarkan, Ikuti, dan Rekam';

  @override
  String get listen_and_follow_desc =>
      'Putar rekaman audio, ikuti skrip untuk meniru pengucapan dan intonasi, lalu rekam suaramu.';

  @override
  String get repeat_the_practice => 'Bandingkan dan Perbaiki';

  @override
  String get repeat_the_practice_desc =>
      'Bandingkan rekaman kamu dengan model audio, tinjau kekuatan dan kelemahan, dan latih terus untuk meningkatkan keterampilan berbicaramu.';

  @override
  String get record_and_compare => 'Rekam dan Bandingkan';

  @override
  String get record_and_compare_desc =>
      'Rekam diri Anda saat berlatih dan bandingkan rekaman Anda dengan model audio untuk mengidentifikasi perbedaan.';

  @override
  String get evaluate_yourself => 'Penilaian Diri';

  @override
  String get evaluate_yourself_desc =>
      'Tinjau rekamanmu, renungkan pengucapan, kelancaran, intonasi, dan kecepatan untuk mengidentifikasi kekuatan dan area yang perlu ditingkatkan.';

  @override
  String get continuous_improvement => 'Perbaikan Terus-menerus';

  @override
  String get continuous_improvement_desc =>
      'Latih dan nilai kinerjamu secara konsisten untuk meningkatkan keterampilan berbicara .';

  @override
  String get profile_settings => 'Profil & Pengaturan';

  @override
  String get edit_profile => 'Edit Profil';

  @override
  String get settings => 'Pengaturan';

  @override
  String get language => 'Bahasa';

  @override
  String get sound => 'Suara';

  @override
  String get dark_theme => 'Tema Gelap';

  @override
  String get membership => 'Keanggotaan';

  @override
  String get transaction_history => 'Riwayat Transaksi';

  @override
  String get logout => 'Keluar';

  @override
  String get notification_settings => 'Pengaturan Notifikasi';

  @override
  String get notification_preferences => 'Preferensi Notifikasi';

  @override
  String get general_notification => 'Notifikasi Umum';

  @override
  String get general_notification_desc =>
      'Terima pembaruan penting dan informasi mengenai akun dan layanan kami.';

  @override
  String get promotion => 'Promosi';

  @override
  String get promotion_desc =>
      'Dapatkan notifikasi tentang penawaran khusus, diskon, dan penawaran eksklusif untuk membantu Anda menghemat.';

  @override
  String get announcement => 'Pengumuman';

  @override
  String get announcement_desc =>
      'Tetap terinformasi tentang fitur baru, pembaruan penting, dan berita tentang layanan kami.';

  @override
  String get study_reminder => 'Pengingat Belajar';

  @override
  String get study_reminder_desc =>
      'Terima pengingat tepat waktu untuk sesi belajar terjadwal Anda agar tetap pada jalur dengan tujuan pembelajaran Anda.';

  @override
  String get notification_info =>
      'Anda dapat mengubah pengaturan ini kapan saja. Beberapa notifikasi mungkin masih dikirimkan untuk pembaruan akun atau keamanan yang penting.';

  @override
  String get do_you_understand => 'Apakah kamu sudah paham?';

  @override
  String get learning_progress => 'Progres Belajar';

  @override
  String get score_acquisition => 'Skor Perolehan';

  @override
  String get excellent => 'Luar biasa!';

  @override
  String get very_good => 'Sangat bagus!👍';

  @override
  String get good => 'Bagus!';

  @override
  String get be_better => 'Ayo tingkatkan!💪';

  @override
  String get fair => 'Cukup lumayan!🙂';

  @override
  String get yes => 'Ya';

  @override
  String get no => 'Tidak';

  @override
  String get how_to_answer => 'Petunjuk';

  @override
  String get cv_instruction_decs1 =>
      'Klik tulisan berwarna abu - abu untuk memutar video.';

  @override
  String get cv_instruction_decs2 => 'untuk memainkan video.';

  @override
  String get cv_instruction_decs3 => 'untuk memberhentikan video.';

  @override
  String get cv_instruction_decs4 =>
      'Kamu juga bisa memutar bagian video yang ingin kamu dengar ulang.';

  @override
  String get cv_instruction_decs5 =>
      'untuk merubah tampilan rasio video menjadi penuh.';

  @override
  String get cv_instruction_decs6 =>
      'untuk keluar dari tampilan video rasio penuh.';

  @override
  String get cv_result =>
      'Yay! Akhirnya kamu berhasil menyelesaikan bagian Video Percakapan ini 🎉';

  @override
  String get click_the_button => 'Klik tombol';

  @override
  String get pc_instruction_decs1 => 'untuk mendengarkan rekaman.';

  @override
  String get pc_instruction_decs2 => 'untuk mengirim suara rekaman.';

  @override
  String get pc_instruction_decs3a => 'Warna tombol berubah';

  @override
  String get pc_instruction_decs3b =>
      'guna membaca rekam suara kamu, kemudian klik kembali untuk kirim.';

  @override
  String get pc_instruction_decs4a => 'Tidak menahan tombol';

  @override
  String get pc_instruction_decs4b => 'guna memproses analisa rekam suaramu.';

  @override
  String get pc_instruction_decs5a => 'Jangan lupa klik tombol';

  @override
  String get pc_instruction_decs5b => 'guna menyelesaikan rangkaian tantangan.';

  @override
  String get pc_instruction_decs6a => 'Kamu juga bisa klik tombol';

  @override
  String get pc_instruction_decs6b =>
      'jika masih ragu dalam menjawab tantangan.';

  @override
  String get lm_instruction_decs1 => 'untuk memutar audio.';

  @override
  String get lm_instruction_decs2 => 'untuk memberhentikan audio.';

  @override
  String get lm_instruction_decs3 =>
      'Kamu juga bisa memutar kembali bagian audio yang ingin kamu dengan ulang.';

  @override
  String get lm_instruction_decs4a => 'Jangan lupa klik tombol';

  @override
  String get lm_instruction_decs4b => 'guna menyelesaikan rangkaian tantangan.';

  @override
  String get lm_instruction_decs5a => 'Kamu juga bisa klik tombol';

  @override
  String get lm_instruction_decs5b =>
      'jika masih ragu dalam menjawab tantangan.';

  @override
  String get record_your_voice => 'Rekam suara anda';

  @override
  String get stage => 'Tahap';

  @override
  String get is_logout_desc => 'Apakah kamu ingin keluar dari aplikasi ini?';

  @override
  String get repeat => 'Ulangi';

  @override
  String get evaluation_results => 'Hasil Evaluasi';

  @override
  String get score_details => 'Detail Skor';

  @override
  String get impressive_work => 'Mengagumkan!🌟';

  @override
  String get bravo => 'Bravo!👏';

  @override
  String get getting_closer => 'Semakin dekat!🔜';

  @override
  String get tackling_a_tough_one => 'Memang cukup sulit!💪';

  @override
  String get interesting_attempt => 'Usaha yang menarik!😄';

  @override
  String get not_quite_there_yet => 'Masih belum!🤔';

  @override
  String get keep_practicing => 'Terus latihan!🔄';

  @override
  String get great_job => 'Kerja hebat!';

  @override
  String get good_effort => 'Usaha bagus!';

  @override
  String get needs_improvement => 'Lebih baik lagi!';

  @override
  String get accuracy => 'Akurasi';

  @override
  String get your_score => 'Skor kamu';

  @override
  String get vocabulary => 'Kosakata';

  @override
  String get part => 'Bagian';

  @override
  String get exercise => 'Latihan';

  @override
  String get your_answer_is_correct => 'Jawaban kamu benar! 🤩🤗';

  @override
  String get your_answer_is_wrong => 'Jawaban kamu salah! 😫😭';

  @override
  String get correct => 'Benar';

  @override
  String get wrong => 'Salah';

  @override
  String get continueYourLessons => 'Lanjutkan Belajar, Capai Tujuan! 📚';

  @override
  String get learning_material =>
      'Ayo selesaikan, gak sabar lihat hasilnya! 🤩';

  @override
  String get explore_your_potential =>
      'Jelajahi potensimu lebih luas lagi 🌍📱📚';

  @override
  String get unlock_opportunities => 'Siap mebuka peluang mendunia?';

  @override
  String get start_journey => 'Saatnya memulai perjalanan belajar';

  @override
  String get get_started => 'Ayo mulai';

  @override
  String get prosody => 'Intonasi dan ritme';

  @override
  String get completeness => 'Kelengkapan';

  @override
  String get unit => 'Unit';

  @override
  String get chapter_list => 'Daftar Chapter';

  @override
  String get more => 'Lebih banyak';

  @override
  String get listening_exercise => 'Latihan Mendengarkan';

  @override
  String get skills_list => 'Daftar Keahlian';

  @override
  String get explore_the_courses => 'Jelajahi Materi Belajar';

  @override
  String get level_pitch_sentences =>
      'Dari komunikasi dasar hingga penguasaan penuh, level-level ini memandu Anda menuju kefasihan berbahasa Inggris.';

  @override
  String get remember_7_items => 'Remember 7 items';

  @override
  String get tap_items_you_saw => 'Tap the items you saw';

  @override
  String get loading => 'Loading';

  @override
  String get ready => 'Ready?';

  @override
  String get go => 'Go!';

  @override
  String get memory_flash_result_desc =>
      'Anda berhasil! Mari kita periksa hasil Anda 📋';

  @override
  String get replay => 'Ulang';

  @override
  String get topic_complete => 'Topik Selesai 📑';

  @override
  String get level_complete => 'Level Selesai🏅 ';

  @override
  String get congratulations => 'Selamat! 🎉';

  @override
  String get score => 'Score';

  @override
  String get select_category => 'Pilih Kategori';

  @override
  String get select_topic => 'Select Topik';

  @override
  String get please_wait => 'Please wait';

  @override
  String get complete_all_challenges => 'Selesaikan Semua Tantangan';

  @override
  String get complete_all_challenges_desc =>
      'Selesaikan semua tantangan untuk melihat nilai akhir Anda.';

  @override
  String get gameplay => 'Gameplay 🕹️';

  @override
  String get how_to_play_memory_flash =>
      'See 7 words for 3 seconds each, then pick them from a list to 10 ( 7 correct + 3 distractors).';

  @override
  String get cert_notif_a1 => '👏 Selamat!\nkamu telah menyelesaikan level A1!';

  @override
  String get cert_notif_a2 =>
      '🌟 Keterampilan Bahasa Inggrismu Berkembang Pesat!';

  @override
  String get cert_notif_b1 =>
      '💪 Mencapai B1 Berarti Anda Dapat Berkomunikasi dengan Lancar!';

  @override
  String get cert_notif_b2 => '🚀 Menyelesaikan B2 Menunjukkan Dedikasimu!';

  @override
  String get cert_notif_c1 =>
      '🔥 Anda Telah Mencapai Penguasaan Tingkat Lanjut!';

  @override
  String get cert_notif_c2 => '👑 Anda Telah Sampai di Puncak!';

  @override
  String get level_not_completed => 'Level Belum Selesai';

  @override
  String get level_not_completed_desc =>
      'Silakan selesaikan semua bab dan bagian di level ini untuk membuka sertifikat Anda.';

  @override
  String get back_to_lessons => 'Kembali ke Pelajaran';

  @override
  String get cert_download_a1 =>
      '🎓 Unduh Sertifikat A1 Anda dan tunjukkan prestasimu!';

  @override
  String get cert_download_a2 =>
      '🎓 Ambil Sertifikat A2 Anda dan bagikan kemajuanmu secara luas!';

  @override
  String get cert_download_b1 =>
      '🎓 Unduh Sertifikat B1 Anda dan inspirasi orang lain untuk terus maju!';

  @override
  String get cert_download_b2 =>
      '🎓 Tunjukkan kesuksesanmu - Sertifikat B2 Anda sudah siap!';

  @override
  String get cert_download_c1 =>
      '🎓 Unduh Sertifikat C1 Anda dan tandai pencapaian besarmu!';

  @override
  String get cert_download_c2 =>
      '🎓 Klaim Sertifikat Penguasaan C2 Anda - Anda pantas mendapatkannya!';

  @override
  String get process_loading =>
      'Tunggu sebentar,\nhasil pengerjaanmu\nmasih diproses 😊';

  @override
  String get certificate_list => 'Daftar Sertifikat';

  @override
  String get recording_error => 'Suara tidak terdeteksi, Silahkan rekam ulang.';

  @override
  String get chapter => 'Unit';

  @override
  String get needs_practice => 'Banyak berlatih!';

  @override
  String get needs_practice_desc => 'Pengucapan masih sering salah.';

  @override
  String get sound_match => '🔊 Akurasi Bunyi';

  @override
  String get smooth_talk => '💬 Kelancaran Ucapan';

  @override
  String get natural_flow => '🎶 Alur Bicara Alami';

  @override
  String get excellent_desc => 'Sudah jelas, tapi masih bisa lebih tepat.';

  @override
  String get good_desc => 'Seperti native speaker.';

  @override
  String get share => 'Bagikan';

  @override
  String get certificates => 'Sertifikat';

  @override
  String get bookmarks => 'Bookmark';

  @override
  String get lm_100_desc => 'Kamu siap\nke level berikutnya! ⏫';

  @override
  String get lm_50_desc => 'Yuk, gas lagi\nbiar makin lancar! 🚀';

  @override
  String get lm_0_desc => 'Terus latihan,\nkamu makin jago! 💪';

  @override
  String get correct_emot => 'Correct ✅';

  @override
  String get wrong_emot => 'Salah ❌';
}
