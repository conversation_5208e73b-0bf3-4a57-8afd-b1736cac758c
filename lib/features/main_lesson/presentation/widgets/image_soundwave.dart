import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/sound_wave_audio_player.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class ImageSoundWave extends StatelessWidget {
  final String imageUrl;
  final String audioUrl;
  final bool isAudioEnabled;
  final bool isLandscape;

  const ImageSoundWave({
    super.key,
    required this.imageUrl,
    required this.audioUrl,
    required this.isAudioEnabled,
    this.isLandscape = false,
  });

  @override
  Widget build(BuildContext context) {
    final double containerWidth = MediaQuery.of(context).size.width - 32;
    final double containerHeight =
        isLandscape ? containerWidth * 0.7 : containerWidth * 1.3;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      width: containerWidth,
      height: containerHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: const Color(0xffFFEDEB),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          Expanded(
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              child: CachedNetworkImage(
                imageUrl: imageUrl,
                fit: isLandscape ? BoxFit.fitWidth : BoxFit.fitHeight,
                width: double.infinity,
                placeholder:
                    (context, url) => Container(
                      color: Colors.grey[300],
                      child: const Center(child: LoadingCircle()),
                    ),
                errorWidget:
                    (context, url, error) => Container(
                      color: Colors.grey[300],
                      child: const Center(child: Icon(Icons.error)),
                    ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: SoundWaveAudioPlayer(
              audioUrl: audioUrl,
              isEnabled: isAudioEnabled,
            ),
          ),
        ],
      ),
    );
  }
}
