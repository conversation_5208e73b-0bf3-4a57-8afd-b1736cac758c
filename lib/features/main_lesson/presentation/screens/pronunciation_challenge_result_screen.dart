import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/pronunciation_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/pronunciation_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/score_progress_indicator.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/incomplete_challenge.dart'; // Added import
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart'; // Added import

class PronunciationChallengeResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;

  const PronunciationChallengeResultScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<PronunciationChallengeResultScreen> createState() =>
      _PronunciationChallengeResultScreenState();
}

class _PronunciationChallengeResultScreenState
    extends ConsumerState<PronunciationChallengeResultScreen>
    with TickerProviderStateMixin {
  late AsyncValue<PronunciationState> viewState;
  late PronunciationController viewModel;
  late final AudioPlayer _bgmPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _playCongrats();
  }

  @override
  void dispose() {
    _bgmPlayer.dispose();
    super.dispose();
  }

  Future<void> _playCongrats() async {
    // Read the audio toggle state
    final isAudioEnabled = ref.read(audioToggleProvider);
    // Only play if audio is enabled
    if (isAudioEnabled) {
      await _bgmPlayer.play(AssetSource('sounds/score.mp3'));
    }
  }

  Widget _buildIncompleteView() {
    return Scaffold(
      body: const IncompleteChallenge(
        assetImagePath: 'assets/images/main_lesson/notcomplete1.png',
      ), // Replaced with the new widget
    );
  }

  Widget _buildResultView(PronunciationState state) {
    final score = state.agregateScore!;
    final pronScore = calculateScore(score.pronScore, score.dataCount);
    final accuracyScore = calculateScore(score.accuracyScore, score.dataCount);
    final fluencyScore = calculateScore(score.fluencyScore, score.dataCount);
    final rhytmScore = calculateScore(score.prosodyScore, score.dataCount);
    final everageScore = calculateAverage([
      accuracyScore,
      fluencyScore,
      rhytmScore,
    ]);

    return Scaffold(
      body: Stack(
        children: [
          Column(
            children: [
              SizedBox(height: 98),
              Text(
                '${context.loc.chapter} ${widget.chapter}',
                style: Theme.of(
                  context,
                ).textTheme.headlineSmall!.copyWith(color: Color(0xff680007)),
              ),
              SizedBox(height: 32),
              Text(
                '$pronScore',
                style: Theme.of(context).textTheme.headlineLarge!.copyWith(
                  fontSize: 70,
                  color:
                      pronScore > 90
                          ? Color(0xff36AA34)
                          : pronScore > 70
                          ? Color(0xffF5BE48)
                          : Color(0xff93000F),
                ),
              ),
              SizedBox(height: 8),
              Text(
                pronScore > 90
                    ? context.loc.excellent
                    : pronScore > 70
                    ? context.loc.good
                    : context.loc.needs_practice,
                style: Theme.of(context).textTheme.headlineLarge,
              ),
              Text(
                pronScore > 90
                    ? context.loc.excellent_desc
                    : pronScore > 70
                    ? context.loc.good_desc
                    : context.loc.needs_practice_desc,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium!.copyWith(color: Color(0xffB4A9A7)),
              ),
              SizedBox(height: 24),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 24),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _scoreValue(
                      score: accuracyScore,
                      description: context.loc.sound_match,
                    ),
                    _scoreValue(
                      score: fluencyScore,
                      description: context.loc.smooth_talk,
                    ),
                    _scoreValue(
                      score: rhytmScore,
                      description: context.loc.natural_flow,
                    ),
                  ],
                ),
              ),
            ],
          ),
          // const SizedBox(height: 66), // Removed as padding is handled inside SingleChildScrollView
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  VButtonGradient(
                    title: context.loc.next,
                    onTap: () => _handleNextButton(),
                    isBorder: false,
                  ),
                  SizedBox(height: 16),
                  VButtonGradient(
                    title: context.loc.share,
                    onTap: () => _handleNextButton(),
                    isBorder: false,
                    // backgroundColor: Colors.white,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                      color: Colors.transparent,
                    ),
                    leading: Icon(Icons.share, color: Color(0xff998E8D)),
                    fontStyle: Theme.of(
                      context,
                    ).textTheme.bodyLarge!.copyWith(color: Color(0xff998E8D)),
                  ),
                ],
              ),
            ),
          ),
          if (state.nextSection) _buildNextSectionDialog(),
        ],
      ),
    );
  }

  Widget _buildNextSectionDialog() {
    return VDialogAlert(
      title: context.loc.nextSection,
      child: Column(
        children: [
          VButtonGradient(
            title: context.loc.yes,
            fontStyle: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.white),
            onTap: () => _navigateToNextSection(),
          ),
          const SizedBox(height: 24),
          VButtonGradient(
            title: context.loc.no,
            fontStyle: Theme.of(context).textTheme.bodyLarge,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14),
              color: Colors.transparent,
              border: Border.all(color: const Color(0xff802115), width: 0.6),
            ),
            onTap: () => context.pop(),
          ),
        ],
      ),
    );
  }

  Future<void> _handleNextButton() async {
    await viewModel.nextQuestion();
    if (viewModel.newSection) {
      viewModel.markSectionAsCompleted();
    } else {
      customNav(
        context,
        RouterName.pronunciationChallenge,
        isReplace: true,
        params: {
          'level': widget.level,
          'chapter': widget.chapter,
          'path': widget.path,
        },
      );
    }
  }

  void _navigateToNextSection() {
    customNav(
      context,
      RouterName.conversationVideoOnboarding,
      isReplace: true,
      params: {'level': widget.level, 'chapter': widget.chapter},
    );
  }

  int calculateScore(double scoreValue, int dataCount) {
    if (dataCount == 0 || scoreValue.isNaN || scoreValue.isInfinite) {
      return 0;
    }
    return (scoreValue / dataCount).round();
  }

  int calculateAverage(List<int> scoreValues) {
    if (scoreValues.isEmpty) {
      return 0;
    }
    final total = scoreValues.fold(0, (sum, value) => sum + value);
    return (total / scoreValues.length).round();
  }

  @override
  Widget build(BuildContext context) {
    final prov = pronunciationControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
    );

    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);
    final score = viewState.value!.agregateScore!;
    final totalContent = viewModel.activePaths.length;

    if (score.dataCount < totalContent) {
      return _buildIncompleteView();
    }

    return _buildResultView(viewState.value!);
  }

  Widget _scoreValue({required int score, required String description}) {
    return Column(
      children: [
        ScoreProgressIndicator(score: score),
        SizedBox(height: 16),
        Text(description, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }
}
