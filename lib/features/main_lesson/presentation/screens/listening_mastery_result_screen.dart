import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/listening_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/listening_state.dart';

import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart'; // Added import

class ListeningMasteryResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  const ListeningMasteryResultScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<ListeningMasteryResultScreen> createState() =>
      _ListeningMasteryResultScreenState();
}

class _ListeningMasteryResultScreenState
    extends ConsumerState<ListeningMasteryResultScreen>
    with TickerProviderStateMixin {
  late AsyncValue<ListeningState> viewState;
  late ListeningController viewModel;
  late final AudioPlayer _bgmPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _playCongrats();
  }

  @override
  void dispose() {
    _bgmPlayer.dispose();
    super.dispose();
  }

  Future<void> _playCongrats() async {
    // Read the audio toggle state
    final isAudioEnabled = ref.read(audioToggleProvider);
    // Only play if audio is enabled
    if (isAudioEnabled) {
      await _bgmPlayer.play(AssetSource('sounds/score.mp3'));
    }
  }

  @override
  Widget build(BuildContext context) {
    final prov = ListeningControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
    );

    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);
    final totalCorrect = viewModel.calculateTotalCorrectPart();
    final totalWrong =
        viewState.value!.currentListenings.questions.length - totalCorrect;
    final score =
        (totalCorrect /
                viewState.value!.currentListenings.questions.length *
                100)
            .round();
    return Scaffold(
      body: Stack(
        children: [
          Column(
            children: [
              SizedBox(height: 98),
              Text(
                '${context.loc.chapter} ${widget.chapter}',
                style: Theme.of(
                  context,
                ).textTheme.headlineSmall!.copyWith(color: Color(0xff680007)),
              ),
              SizedBox(height: 32),
              Text(
                '$score',
                style: Theme.of(context).textTheme.headlineLarge!.copyWith(
                  fontSize: 70,
                  color:
                      score > 85
                          ? Color(0xff36AA34)
                          : score > 60
                          ? Color(0xffF5BE48)
                          : Color(0xff93000F),
                ),
              ),
              SizedBox(height: 8),
              Text(
                score > 85
                    ? context.loc.lm_100_desc
                    : score > 60
                    ? context.loc.lm_50_desc
                    : context.loc.lm_0_desc,
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 24),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: _correctWrongItem(
                        title: context.loc.correct_emot,
                        count: totalCorrect,
                        color: const Color(0xff36AA34),
                      ),
                    ),
                    Expanded(
                      child: _correctWrongItem(
                        title: context.loc.wrong_emot,
                        count: totalWrong,
                        color: const Color(0xff93000F),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // const SizedBox(height: 66), // Removed as padding is handled inside SingleChildScrollView
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  VButtonGradient(
                    title: context.loc.next,
                    onTap: () => _handleNextButton(),
                    isBorder: false,
                  ),
                  SizedBox(height: 16),
                  VButtonGradient(
                    title: context.loc.share,
                    onTap: () => _handleNextButton(),
                    isBorder: false,
                    // backgroundColor: Colors.white,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                      color: Colors.transparent,
                    ),
                    leading: Icon(Icons.share, color: Color(0xff998E8D)),
                    fontStyle: Theme.of(
                      context,
                    ).textTheme.bodyLarge!.copyWith(color: Color(0xff998E8D)),
                  ),
                ],
              ),
            ),
          ),
          if (viewState.value!.nextSection) _buildNextSectionDialog(),
        ],
      ),
    );
  }

  Widget _correctWrongItem({
    required String title,
    required int count,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: .4),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(width: 0.6, color: color),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium!.copyWith(
              color: const Color(0xffB4A9A7),
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          Text(
            '$count',
            style: Theme.of(
              context,
            ).textTheme.headlineLarge!.copyWith(color: color),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNextSectionDialog() {
    return VDialogAlert(
      title: context.loc.nextSection,
      child: Column(
        children: [
          VButtonGradient(
            title: context.loc.yes,
            fontStyle: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.white),
            onTap: () async {
              // First, reset the nextSection state to hide the dialog
              viewModel.resetNextSectionState();
              // Wait a frame to ensure the dialog is removed from the widget tree
              await Future.delayed(const Duration(milliseconds: 100));
              // Then proceed with navigation
              if (mounted) {
                viewModel.markSectionAsCompleted();
                // Navigate to speaking arena only when we've completed all listening content
                customNav(
                  context,
                  RouterName.speakingArenaOnboarding,
                  isReplace: true,
                  params: {'level': widget.level, 'chapter': widget.chapter},
                );
              }
            },
          ),
          const SizedBox(height: 24),
          VButtonGradient(
            title: context.loc.no,
            fontStyle: Theme.of(context).textTheme.bodyLarge,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14),
              color: Colors.transparent,
              border: Border.all(color: const Color(0xff802115), width: 0.6),
            ),
            onTap: () => context.pop(),
          ),
        ],
      ),
    );
  }

  Future<void> _handleNextButton() async {
    await viewModel.nextPart(context);
    if (viewState.value!.nextSection) {
      viewModel.markSectionAsCompleted();
    } else {}
  }
}
